#!/bin/bash

echo "🎵 Setting up Spotify Music Downloader..."
echo ""

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "⚠️  This setup script is designed for macOS"
    echo "   You may need to modify the commands for your OS"
    echo ""
fi

# Install spotdl
echo "📦 Installing spotdl..."
pip3 install spotdl
if [ $? -ne 0 ]; then
    echo "❌ Failed to install spotdl. Make sure Python 3 and pip are installed."
    exit 1
fi

# Check for Homebrew and install FFmpeg
echo "🔧 Checking for FFmpeg..."
if ! command -v ffmpeg &> /dev/null; then
    echo "📦 FFmpeg not found. Installing via Homebrew..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "📦 Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        
        # Add Homebrew to PATH
        echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc
        export PATH="/opt/homebrew/bin:$PATH"
    fi
    
    # Install FFmpeg
    brew install ffmpeg
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install FFmpeg"
        exit 1
    fi
else
    echo "✅ FFmpeg is already installed"
fi

# Create directories
echo "📁 Creating directories..."
mkdir -p ~/bin
mkdir -p ~/.spotdl

# Copy scripts
echo "📋 Installing scripts..."
<<<<<<< HEAD
cp bin/spotdl-batch ~/bin/
chmod +x ~/bin/spotdl-batch
=======
cp bin/spotdl ~/bin/
cp bin/spotdl-batch ~/bin/
chmod +x ~/bin/spotdl ~/bin/spotdl-batch
>>>>>>> e14734ee1fc7b3e67297d7a95993f78a3c3d684f

# Copy config
echo "⚙️  Installing configuration..."
cp config/config.json ~/.spotdl/

# Copy links template to Music folder
echo "📝 Setting up links template..."
cp config/spotify_links.txt ~/Music/

# Add to PATH if not already there
echo "🔧 Updating PATH..."
if ! grep -q 'export PATH="$HOME/bin:$PATH"' ~/.zshrc; then
    echo 'export PATH="$HOME/bin:$PATH"' >> ~/.zshrc
fi

if ! grep -q 'export PATH="/opt/homebrew/bin:$PATH"' ~/.zshrc; then
    echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Restart your terminal or run: source ~/.zshrc"
echo "   2. Add Spotify links to ~/Music/spotify_links.txt"
echo "   3. Run: spotdl-batch"
echo ""
echo "💡 Usage:"
<<<<<<< HEAD
echo "   Single download: python3 -m spotdl \"https://open.spotify.com/track/...\""
=======
echo "   Single download: spotdl \"https://open.spotify.com/track/...\""
>>>>>>> e14734ee1fc7b3e67297d7a95993f78a3c3d684f
echo "   Batch download:  spotdl-batch"
echo ""
echo "📁 All music will be saved to: ~/Music/"
echo "🔄 Duplicate songs are automatically skipped!"
