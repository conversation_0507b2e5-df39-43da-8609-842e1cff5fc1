#!/usr/bin/env python3
"""
Monsterbook metadata collection with known API responses
"""

import json
import csv
from urllib.parse import urlparse

# Known API responses (manually collected)
KNOWN_RESPONSES = {
    "THEANEMONE": "monsterbook.io/json/theaAAMLCQ0sDJcpWmXX.json",
    "CDABYSSINIAN": "monsterbook.io/json/cdab69GMAotvOvof62nj.json",
    "BIGBEAKBIRD": "monsterbook.io/json/bigbUwZ3btO22xqn4H3N.json",
    "BEGGERM": "monsterbook.io/json/begg6iAFGOFgI3ItzkTz.json",
    "BINACARD": "monsterbook.io/json/bina8mQKdTDfJPj9Ht75.json",
    "BONECLAD": "monsterbook.io/json/boneyayzfoBaH25tA168.json",
    "FOUNDERCARD": "monsterbook.io/json/founXoSXmvogoIee5QWM.json",
    "SIIDZ": "monsterbook.io/json/siidVFJkD7Ntva3nt2Jf.json",
}

# All asset names
ASSET_NAMES = [
    "CDABYSSINIAN", "THEANEMONE", "CDAURGELMIR", "BEGGERM", "BIGBEAKBIRD", "BINACARD",
    "BONECLAD", "CAVENAGA", "CHILDOFLIGHT", "CLOCHET", "THEROCKCOCK", "COLLECTDRONE",
    "CRUNCHDRONE", "CYRUSCARD", "DGELLIES", "SECURBOT", "DOGGYBAG", "DOLEAPHUS",
    "DROG", "FAHL", "FARMERMAGPIE", "FENFIRE", "FENGBO", "FIREFIEND", "FLOGENGINEER",
    "FORGETTEI", "HENGE", "FYRR", "GALARSMONK", "GALDALS", "GENTLEGHOST", "GERMOGLIO",
    "GIGAJELLY", "GLOOMBUG", "GLYME", "GOLEMSOLDIER", "GROWNUPSIIDZ", "GRUMPYDOG",
    "GYARU", "HAMMERHEAD", "SIIDZ", "HORNJAW", "HUNTERRAY", "HUNTERBOT", "HUNTINGDRONE",
    "FISHERJEAN", "JUHNPOWDER", "KAGUTSUCHI", "KINGOKO", "KITSUNEBI", "KINGOKOGUARD",
    "LIRCARD", "LOUFMECHA", "LURKCARD", "MAKARA", "MARVINCARD", "MAUGMIGHT", "MAXTHECAT",
    "MOPEY", "MOSU", "MRBRAINER", "MUUWY", "NAIAD", "OCTOPHIBIUS", "OMNIBITE",
    "ONEEYEGOOBER", "PEACESIIDZ", "PUFFCARD", "ROBSHY", "RADARCARD", "RABBOX",
    "RAWRAFARR", "REBITZ", "KAMAKHAZI", "SANDALPHON", "SAWBILL", "SCOUTDRONE",
    "SEEDOFDRAGON", "SHREIKFLOWER", "SHUURI", "SILOBOT", "SLIVERHUNTER", "SMOLGERM",
    "SHREEIK", "TATZELWORM", "TEENFLARWAS", "CANDLEMAKER", "GRAILRARE", "THESLAYER",
    "THESNEER", "TOOTHBOOTH", "TOOTHOZORUS", "TRICKCARD", "UNIWHALE", "URNACH",
    "VISIONBRINGR", "WAFFLESCARD", "WEAVERS", "WILOHCHOMP", "CHAMEO", "TARTHORK",
    "OCTOLGA", "FOUNDERCARD"
]

def normalize_url(url):
    """Normalize URL by adding http:// if missing protocol"""
    if not url:
        return ""
    
    url = url.strip()
    # Handle escaped slashes
    url = url.replace('\\/', '/')
    
    if not url.startswith(('http://', 'https://')):
        url = 'http://' + url
    return url

def extract_json_filename(url):
    """Extract the JSON filename from a URL"""
    if not url:
        return ""
    
    # Parse the URL and get the path
    parsed = urlparse(url)
    path = parsed.path
    
    # Extract the filename (last segment after /)
    filename = path.split('/')[-1]
    
    # Ensure it's a .json file
    if filename.endswith('.json'):
        return filename
    
    return ""

def generate_files():
    """Generate the required output files"""
    print("🚀 Generating Monsterbook metadata files...")
    print(f"📊 Processing {len(ASSET_NAMES)} assets")
    print(f"✅ Known responses: {len(KNOWN_RESPONSES)}")
    print()
    
    results = []
    json_filenames = []
    errors = []
    
    for i, asset_name in enumerate(ASSET_NAMES, 1):
        print(f"[{i:3d}/{len(ASSET_NAMES)}] Processing {asset_name}...")
        
        # Construct API URL
        api_url = f"https://tokenscan.io/api/asset/{asset_name}"
        
        # Get description if known
        description = KNOWN_RESPONSES.get(asset_name, "")
        
        if description:
            # Normalize the URL
            normalized_url = normalize_url(description)
            
            # Extract JSON filename
            json_filename = extract_json_filename(normalized_url)
            
            if json_filename:
                json_filenames.append(json_filename)
            
            print(f"     ✅ Found: {json_filename}")
        else:
            normalized_url = ""
            errors.append(asset_name)
            print(f"     ❌ Missing API data")
        
        # Add to results
        results.append({
            'asset': asset_name,
            'edition': '',  # Will be filled from spreadsheet if available
            'tokenscan_api_url': api_url,
            'description_json_url': normalized_url
        })
    
    # Generate output files
    print("\n📝 Generating output files...")
    
    # 1. CSV file
    csv_file = 'monsterbook_metadata.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['asset', 'edition', 'tokenscan_api_url', 'description_json_url'])
        writer.writeheader()
        writer.writerows(results)
    print(f"✅ Created {csv_file}")
    
    # 2. JSON filenames manifest
    manifest_file = 'json_filenames.txt'
    with open(manifest_file, 'w', encoding='utf-8') as f:
        for filename in sorted(json_filenames):
            f.write(f"{filename}\n")
    print(f"✅ Created {manifest_file} with {len(json_filenames)} filenames")
    
    # 3. Errors log
    errors_file = 'errors.log'
    with open(errors_file, 'w', encoding='utf-8') as f:
        if errors:
            f.write("Assets missing API data (need to be fetched manually):\n")
            for asset in errors:
                f.write(f"{asset}\n")
        else:
            f.write("No errors - all assets have API data.\n")
    print(f"✅ Created {errors_file}")
    
    # 4. Missing assets list for manual fetching
    missing_file = 'missing_assets.txt'
    with open(missing_file, 'w', encoding='utf-8') as f:
        f.write("Assets that need manual API fetching:\n")
        f.write("=====================================\n\n")
        for asset in errors:
            f.write(f"web-fetch: https://tokenscan.io/api/asset/{asset}\n")
    print(f"✅ Created {missing_file}")
    
    # Summary
    print()
    print("📊 SUMMARY:")
    print(f"   Total assets: {len(ASSET_NAMES)}")
    print(f"   Known responses: {len(KNOWN_RESPONSES)}")
    print(f"   Missing data: {len(errors)}")
    print(f"   JSON files found: {len(json_filenames)}")
    
    if errors:
        print(f"\n⚠️  Need to fetch {len(errors)} more assets manually")
        print("   Use the missing_assets.txt file for web-fetch commands")
    
    print("\n🎉 File generation complete!")

if __name__ == "__main__":
    generate_files()
