#!/usr/bin/env python3
"""
Monsterbook JSON Metadata Recovery Script

Fetches metadata URLs from TokenScan API for all Monsterbook assets
and generates the required output files for host migration.
"""

import requests
import time
import csv
import json
import re
from urllib.parse import urlparse

# Asset names from the provided list
ASSET_NAMES = [
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "B<PERSON><PERSON>R<PERSON>", "BIGBEAK<PERSON>RD", "BINACARD",
    "BONECLAD", "CAVENAGA", "CHILDOFLIGHT", "CLOCHET", "THEROCKCOCK", "COLLECT<PERSON>ON<PERSON>",
    "CRUNCH<PERSON>ON<PERSON>", "CYR<PERSON>CA<PERSON>", "DGELLIE<PERSON>", "SECURBOT", "DOGGYBAG", "DOLEAPHUS",
    "DROG", "FAHL", "FARMERMAGPIE", "FENFIRE", "FENGBO", "FIREFIE<PERSON>", "FLOGENGINEER",
    "FOR<PERSON><PERSON><PERSON>", "<PERSON>EN<PERSON>", "<PERSON>YR<PERSON>", "GALARSMONK", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "G<PERSON>OOMBUG", "GLYME", "GOLEMSOLDIER", "GROWNUPSIIDZ", "GRUMPYDOG",
    "GYARU", "HAMMERHEAD", "SIIDZ", "HORNJAW", "HUNTERRAY", "HUNTERBOT", "HUNTINGDRONE",
    "FISHERJEAN", "JUHNPOWDER", "KAGUTSUCHI", "KINGOKO", "KITSUNEBI", "KINGOKOGUARD",
    "LIRCARD", "LOUFMECHA", "LURKCARD", "MAKARA", "MARVINCARD", "MAUGMIGHT", "MAXTHECAT",
    "MOPEY", "MOSU", "MRBRAINER", "MUUWY", "NAIAD", "OCTOPHIBIUS", "OMNIBITE",
    "ONEEYEGOOBER", "PEACESIIDZ", "PUFFCARD", "ROBSHY", "RADARCARD", "RABBOX",
    "RAWRAFARR", "REBITZ", "KAMAKHAZI", "SANDALPHON", "SAWBILL", "SCOUTDRONE",
    "SEEDOFDRAGON", "SHREIKFLOWER", "SHUURI", "SILOBOT", "SLIVERHUNTER", "SMOLGERM",
    "SHREEIK", "TATZELWORM", "TEENFLARWAS", "CANDLEMAKER", "GRAILRARE", "THESLAYER",
    "THESNEER", "TOOTHBOOTH", "TOOTHOZORUS", "TRICKCARD", "UNIWHALE", "URNACH",
    "VISIONBRINGR", "WAFFLESCARD", "WEAVERS", "WILOHCHOMP", "CHAMEO", "TARTHORK",
    "OCTOLGA", "FOUNDERCARD"
]

# Configuration
API_BASE_URL = "https://tokenscan.io/api/asset/"
REQUEST_DELAY = 0.25  # 250ms delay between requests
MAX_RETRIES = 2
TIMEOUT = 10

def normalize_url(url):
    """Normalize URL by adding http:// if missing protocol"""
    if not url:
        return ""
    
    url = url.strip()
    if not url.startswith(('http://', 'https://')):
        url = 'http://' + url
    return url

def extract_json_filename(url):
    """Extract the JSON filename from a URL"""
    if not url:
        return ""
    
    # Parse the URL and get the path
    parsed = urlparse(url)
    path = parsed.path
    
    # Extract the filename (last segment after /)
    filename = path.split('/')[-1]
    
    # Ensure it's a .json file
    if filename.endswith('.json'):
        return filename
    
    return ""

def fetch_asset_description(asset_name, retries=MAX_RETRIES):
    """Fetch description from TokenScan API for a given asset"""
    url = f"{API_BASE_URL}{asset_name}"
    
    for attempt in range(retries + 1):
        try:
            print(f"Fetching {asset_name} (attempt {attempt + 1}/{retries + 1})...")
            
            response = requests.get(url, timeout=TIMEOUT)
            response.raise_for_status()
            
            data = response.json()
            description = data.get('description', '')
            
            if description:
                print(f"✅ {asset_name}: Found description")
                return description
            else:
                print(f"⚠️  {asset_name}: No description field in response")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {asset_name}: Request failed - {e}")
            if attempt < retries:
                print(f"   Retrying in {REQUEST_DELAY}s...")
                time.sleep(REQUEST_DELAY)
            else:
                print(f"   Max retries exceeded")
                return None
        except json.JSONDecodeError as e:
            print(f"❌ {asset_name}: Invalid JSON response - {e}")
            return None
        except Exception as e:
            print(f"❌ {asset_name}: Unexpected error - {e}")
            return None
    
    return None

def main():
    """Main function to process all assets and generate output files"""
    print("🚀 Starting Monsterbook metadata recovery...")
    print(f"📊 Processing {len(ASSET_NAMES)} assets")
    print(f"⏱️  Using {REQUEST_DELAY}s delay between requests")
    print()
    
    results = []
    errors = []
    json_filenames = []
    
    for i, asset_name in enumerate(ASSET_NAMES, 1):
        print(f"[{i}/{len(ASSET_NAMES)}] Processing {asset_name}...")
        
        # Construct API URL
        api_url = f"{API_BASE_URL}{asset_name}"
        
        # Fetch description
        description = fetch_asset_description(asset_name)
        
        if description:
            # Normalize the URL
            normalized_url = normalize_url(description)
            
            # Extract JSON filename
            json_filename = extract_json_filename(normalized_url)
            
            if json_filename:
                json_filenames.append(json_filename)
            
            # Add to results
            results.append({
                'asset': asset_name,
                'edition': '',  # Will be filled from spreadsheet if available
                'tokenscan_api_url': api_url,
                'description_json_url': normalized_url
            })
            
            print(f"   📄 JSON file: {json_filename}")
            
        else:
            # Add to errors
            errors.append(asset_name)
            
            # Add empty result
            results.append({
                'asset': asset_name,
                'edition': '',
                'tokenscan_api_url': api_url,
                'description_json_url': ''
            })
        
        print()
        
        # Delay between requests (except for last one)
        if i < len(ASSET_NAMES):
            time.sleep(REQUEST_DELAY)
    
    # Generate output files
    print("📝 Generating output files...")
    
    # 1. CSV file
    csv_file = 'monsterbook_metadata.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=['asset', 'edition', 'tokenscan_api_url', 'description_json_url'])
        writer.writeheader()
        writer.writerows(results)
    print(f"✅ Created {csv_file}")
    
    # 2. Excel file (create manually without pandas)
    excel_file = 'monsterbook_metadata.xlsx'
    try:
        import openpyxl
        from openpyxl import Workbook

        wb = Workbook()
        ws = wb.active
        ws.title = "Monsterbook Metadata"

        # Add headers
        headers = ['asset', 'edition', 'tokenscan_api_url', 'description_json_url']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # Add data
        for row, result in enumerate(results, 2):
            ws.cell(row=row, column=1, value=result['asset'])
            ws.cell(row=row, column=2, value=result['edition'])
            ws.cell(row=row, column=3, value=result['tokenscan_api_url'])
            ws.cell(row=row, column=4, value=result['description_json_url'])

        wb.save(excel_file)
        print(f"✅ Created {excel_file}")
    except ImportError:
        print(f"⚠️  Skipping Excel file creation (openpyxl not available)")
        print(f"   You can convert the CSV to Excel manually")
    
    # 3. JSON filenames manifest
    manifest_file = 'json_filenames.txt'
    with open(manifest_file, 'w', encoding='utf-8') as f:
        for filename in sorted(json_filenames):
            f.write(f"{filename}\n")
    print(f"✅ Created {manifest_file} with {len(json_filenames)} filenames")
    
    # 4. Errors log
    errors_file = 'errors.log'
    with open(errors_file, 'w', encoding='utf-8') as f:
        if errors:
            f.write("Assets with API errors or missing descriptions:\n")
            for asset in errors:
                f.write(f"{asset}\n")
        else:
            f.write("No errors encountered.\n")
    print(f"✅ Created {errors_file}")
    
    # Summary
    print()
    print("📊 SUMMARY:")
    print(f"   Total assets processed: {len(ASSET_NAMES)}")
    print(f"   Successful: {len(results) - len(errors)}")
    print(f"   Errors: {len(errors)}")
    print(f"   JSON files found: {len(json_filenames)}")
    
    if errors:
        print(f"\n❌ Assets with errors: {', '.join(errors)}")
    
    print("\n🎉 Metadata recovery complete!")
    print("\nNext steps:")
    print("1. Review the generated files")
    print("2. Upload the JSON files to monsterbook.io/json/ directory")
    print("3. Test a few URLs to ensure they're working")

if __name__ == "__main__":
    main()
