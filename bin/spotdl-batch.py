#!/usr/bin/env python3
"""
Spotify Batch Downloader - Python Version
Reliable parallel downloader with comprehensive logging and error handling
"""

import argparse
import asyncio
import json
import logging
import os
import re
import subprocess
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple

# Configuration
DEFAULT_LINKS_FILE = Path.home() / "Music/spotify_links.txt"
DOWNLOAD_LOG = Path.home() / "Music/download_log.txt"
MUSIC_DIR = Path.home() / "Music"
MAX_PARALLEL_JOBS = 1  # Conservative: 1 album at a time to avoid YouTube rate limits
MAX_THREADS_PER_JOB = 1  # Single thread to minimize API requests
TIMEOUT_SECONDS = 45   # 45 seconds per album - aggressive timeout
RETRY_COUNT = 2

class SpotDLBatch:
    def __init__(self, links_file: Path, parallel_jobs: int = MAX_PARALLEL_JOBS, 
                 threads_per_job: int = MAX_THREADS_PER_JOB, force_redownload: bool = False):
        self.links_file = links_file
        self.parallel_jobs = parallel_jobs
        self.threads_per_job = threads_per_job
        self.force_redownload = force_redownload
        self.download_log = DOWNLOAD_LOG
        
        # Setup logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # Initialize download log
        self._init_download_log()
        
    def _init_download_log(self):
        """Initialize the download log file"""
        with open(self.download_log, 'a') as f:
            f.write(f"\n=== DOWNLOAD SESSION STARTED: {datetime.now()} ===\n")
    
    def _log_to_file(self, message: str):
        """Log message to download log file"""
        with open(self.download_log, 'a') as f:
            f.write(f"[{datetime.now()}] {message}\n")
    
    def get_spotify_links(self) -> List[str]:
        """Extract Spotify links from file, reversed order (bottom to top)"""
        if not self.links_file.exists():
            raise FileNotFoundError(f"Links file not found: {self.links_file}")
        
        with open(self.links_file, 'r') as f:
            lines = f.readlines()
        
        # Extract Spotify URLs and reverse order
        spotify_links = []
        for line in lines:
            line = line.strip()
            if line.startswith('https://open.spotify.com'):
                spotify_links.append(line)
        
        return list(reversed(spotify_links))  # Bottom to top as requested
    
    def download_single_link(self, link: str, job_id: int, total_links: int = 0) -> Dict:
        """Download a single Spotify link with timeout and error handling"""
        start_time = time.time()
        progress_msg = f"[Album {job_id}/{total_links}]" if total_links > 0 else f"[Job {job_id}]"
        self.logger.info(f"{progress_msg} Starting: {link}")
        self._log_to_file(f"{progress_msg} Starting: {link}")
        
        # Build command
        cmd = [
            'python3', '-m', 'spotdl', '--config',
            '--threads', str(self.threads_per_job),
            '--overwrite', 'force' if self.force_redownload else 'skip',
            'download', link
        ]
        
        if not self.force_redownload:
            cmd.insert(-2, '--scan-for-songs')
        
        result = {
            'job_id': job_id,
            'link': link,
            'success': False,
            'downloaded': 0,
            'skipped': 0,
            'failed': 0,
            'expected': 0,
            'error': None,
            'duration': 0,
            'output': ''
        }
        
        for attempt in range(RETRY_COUNT + 1):
            if attempt > 0:
                self.logger.info(f"[Job {job_id}] Retry {attempt}/{RETRY_COUNT}")
                time.sleep(2)  # Brief delay before retry
            
            try:
                # Run with timeout
                process = subprocess.Popen(
                    cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                    text=True, bufsize=1, universal_newlines=True
                )
                
                output_lines = []
                start_time = time.time()
                
                last_output_time = time.time()
                stall_timeout = 20  # Kill if no output for 20 seconds

                while True:
                    # Check if process is still running
                    if process.poll() is not None:
                        break

                    current_time = time.time()

                    # Check for total timeout
                    if current_time - start_time > TIMEOUT_SECONDS:
                        self.logger.warning(f"[Job {job_id}] Total timeout after {TIMEOUT_SECONDS}s, killing process")
                        process.kill()
                        result['error'] = f"Total timeout after {TIMEOUT_SECONDS} seconds"
                        break

                    # Check for stall timeout (no output)
                    if current_time - last_output_time > stall_timeout:
                        self.logger.warning(f"[Job {job_id}] Stalled (no output for {stall_timeout}s), killing process")
                        process.kill()
                        result['error'] = f"Stalled - no output for {stall_timeout} seconds"
                        break

                    # Read output with timeout
                    try:
                        line = process.stdout.readline()
                        if line:
                            output_lines.append(line.strip())
                            last_output_time = current_time
                            # Print progress in real-time
                            print(f"[Job {job_id}] {line.strip()}")
                        else:
                            time.sleep(0.1)
                    except Exception as e:
                        self.logger.error(f"[Job {job_id}] Error reading output: {e}")
                        break
                
                # Get final output
                remaining_output, _ = process.communicate()
                if remaining_output:
                    output_lines.extend(remaining_output.strip().split('\n'))
                
                result['output'] = '\n'.join(output_lines)
                result['duration'] = time.time() - start_time
                
                if process.returncode == 0:
                    result['success'] = True
                    # Parse output for statistics
                    self._parse_output(result, output_lines)
                    break
                else:
                    # Check for rate limiting
                    output_text = '\n'.join(output_lines)
                    if 'rate' in output_text.lower() or 'limit' in output_text.lower():
                        self.logger.warning(f"[Job {job_id}] Rate limit detected, pausing 30 seconds...")
                        time.sleep(30)
                    result['error'] = f"Process failed with return code {process.returncode}"
                    
            except Exception as e:
                result['error'] = f"Exception: {str(e)}"
                self.logger.error(f"[Job {job_id}] Exception: {e}")
        
        # Log result
        if result['success']:
            self.logger.info(f"[Job {job_id}] ✅ Completed successfully!")
            self._log_to_file(f"[Job {job_id}] SUCCESS: {link} - Downloaded: {result['downloaded']}, Skipped: {result['skipped']}, Failed: {result['failed']}")
        else:
            self.logger.error(f"[Job {job_id}] ❌ Failed: {result['error']}")
            self._log_to_file(f"[Job {job_id}] FAILED: {link} - Error: {result['error']}")
        
        return result
    
    def _parse_output(self, result: Dict, output_lines: List[str]):
        """Parse spotdl output to extract statistics"""
        output_text = '\n'.join(output_lines)

        # Look for "Found X songs in Album" pattern
        found_match = re.search(r'Found (\d+) songs in', output_text)
        if found_match:
            result['expected'] = int(found_match.group(1))

        # Count skipped songs more precisely
        skipped_patterns = [
            r'Skipping.*\(file already exists\)',
            r'INFO:spotdl\.download\.downloader:Skipping.*\(file already exists\)'
        ]
        skipped_count = 0
        for pattern in skipped_patterns:
            skipped_count += len(re.findall(pattern, output_text))
        result['skipped'] = skipped_count

        # Count actually downloaded songs (look for download completion indicators)
        download_patterns = [
            r'Downloaded.*\.flac',
            r'✓.*\.flac',
            r'Successfully downloaded',
            r'Download completed'
        ]
        downloaded_count = 0
        for pattern in download_patterns:
            downloaded_count += len(re.findall(pattern, output_text))
        result['downloaded'] = downloaded_count

        # Count failed songs more precisely
        failed_patterns = [
            r'Failed to download',
            r'ERROR:.*download',
            r'LookupError: No results found',
            r'❌.*failed'
        ]
        failed_count = 0
        for pattern in failed_patterns:
            failed_count += len(re.findall(pattern, output_text))
        result['failed'] = failed_count

        # If we have expected count but no individual counts, try to infer
        if result['expected'] > 0 and (result['downloaded'] + result['skipped'] + result['failed']) == 0:
            # If all songs were skipped (common case)
            if 'file already exists' in output_text:
                result['skipped'] = result['expected']
    
    def run(self):
        """Main execution function"""
        try:
            # Get links
            links = self.get_spotify_links()
            if not links:
                self.logger.error("No Spotify links found in file")
                return
            
            self.logger.info(f"🎵 Starting parallel batch download")
            self.logger.info(f"📁 Source: {self.links_file}")
            self.logger.info(f"🎯 Destination: {MUSIC_DIR}")
            self.logger.info(f"📝 Download log: {self.download_log}")
            self.logger.info(f"⚡ Parallel jobs: {self.parallel_jobs}")
            self.logger.info(f"🔥 Threads per job: {self.threads_per_job}")
            self.logger.info(f"📊 Found {len(links)} Spotify links to process")
            self.logger.info(f"🎯 Estimated total albums: {len(links)}")

            if self.force_redownload:
                self.logger.info("🔄 Force redownload mode: Will redownload all songs")
            else:
                self.logger.info("🔍 Using smart duplicate detection...")

            self.logger.info("⚠️  Using conservative settings to avoid YouTube rate limits")
            
            # Process links in parallel
            results = []
            total_links = len(links)
            with ThreadPoolExecutor(max_workers=self.parallel_jobs) as executor:
                # Submit all jobs
                future_to_job = {
                    executor.submit(self.download_single_link, link, i + 1, total_links): (link, i + 1)
                    for i, link in enumerate(links)
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_job):
                    link, job_id = future_to_job[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        self.logger.error(f"Job {job_id} generated an exception: {e}")
                        results.append({
                            'job_id': job_id,
                            'link': link,
                            'success': False,
                            'error': str(e),
                            'downloaded': 0,
                            'skipped': 0,
                            'failed': 0,
                            'expected': 0
                        })
            
            # Calculate and display summary
            self._display_summary(results)
            
        except Exception as e:
            self.logger.error(f"Fatal error: {e}")
            self._log_to_file(f"FATAL ERROR: {e}")
    
    def _display_summary(self, results: List[Dict]):
        """Display final summary statistics"""
        successful_links = sum(1 for r in results if r['success'])
        failed_links = len(results) - successful_links
        
        total_downloaded = sum(r['downloaded'] for r in results)
        total_skipped = sum(r['skipped'] for r in results)
        total_failed = sum(r['failed'] for r in results)
        total_expected = sum(r['expected'] for r in results)
        
        print("\n🎉 Batch processing complete!")
        print("=" * 60)
        print("📊 DOWNLOAD SUMMARY:")
        print(f"   🆕 NEW SONGS DOWNLOADED: {total_downloaded}")
        print(f"   ⏭️  Songs already existed (skipped): {total_skipped}")
        print(f"   ❌ Songs failed to download: {total_failed}")
        print(f"   📈 Total songs found: {total_expected}")
        print(f"   📀 Total songs processed: {total_downloaded + total_skipped + total_failed}")
        print()
        print(f"   ✅ Albums/Links processed successfully: {successful_links}")
        print(f"   ❌ Albums/Links failed: {failed_links}")
        print("=" * 60)

        if total_downloaded > 0:
            print(f"🎉 SUCCESS: Downloaded {total_downloaded} new songs!")
        else:
            print("ℹ️  No new songs downloaded - all songs already exist")
            if total_failed > 0:
                print(f"⚠️  {total_failed} songs failed - check incomplete albums")

        print(f"🎵 Check your Music folder: {MUSIC_DIR}")

        if not self.force_redownload:
            print("💡 Note: Existing songs were automatically skipped")
            print("💡 Use '--force' to redownload everything")

        print()
        print("🔥 Performance Stats:")
        print(f"   ⚡ {self.threads_per_job * self.parallel_jobs} total concurrent threads")
        print(f"   🎯 Used {self.parallel_jobs} parallel jobs with {self.threads_per_job} threads each")
        print(f"📝 Download log saved to: {self.download_log}")
        
        # Log final summary
        self._log_to_file("=== DOWNLOAD SESSION COMPLETED ===")
        self._log_to_file(f"Songs downloaded: {total_downloaded}, Skipped: {total_skipped}, Failed: {total_failed}")
        self._log_to_file(f"Links processed: {successful_links}, Links failed: {failed_links}")
        self._log_to_file("")

def main():
    parser = argparse.ArgumentParser(description="🎵 spotdl-batch - Spotify Batch Downloader")
    parser.add_argument('file', nargs='?', default=DEFAULT_LINKS_FILE,
                       help='Path to file containing Spotify links')
    parser.add_argument('--force', '-f', action='store_true',
                       help='Force redownload even if files exist')
    parser.add_argument('--incomplete', '-i', action='store_true',
                       help='Only download incomplete albums (skip albums where all songs exist)')
    parser.add_argument('--jobs', '-j', type=int, default=MAX_PARALLEL_JOBS,
                       help=f'Parallel downloads (default: {MAX_PARALLEL_JOBS})')
    parser.add_argument('--threads', '-t', type=int, default=MAX_THREADS_PER_JOB,
                       help=f'Threads per download (default: {MAX_THREADS_PER_JOB})')

    args = parser.parse_args()
    
    # Convert to Path object
    links_file = Path(args.file)
    
    # Create and run downloader
    downloader = SpotDLBatch(
        links_file=links_file,
        parallel_jobs=args.jobs,
        threads_per_job=args.threads,
        force_redownload=args.force
    )
    
    downloader.run()

if __name__ == "__main__":
    main()
