#!/usr/bin/env python3
"""
Simple Spotify Batch Downloader - No hanging, aggressive timeouts
"""

import argparse
import logging
import subprocess
import time
from datetime import datetime
from pathlib import Path

# Configuration
DEFAULT_LINKS_FILE = Path.home() / "Music/spotify_links.txt"
DOWNLOAD_LOG = Path.home() / "Music/download_log.txt"
MUSIC_DIR = Path.home() / "Music"
TIMEOUT_SECONDS = 20  # EXTREMELY aggressive 20 second timeout
RETRY_COUNT = 1  # Only 1 retry to avoid wasting time

class SimpleSpotDL:
    def __init__(self, links_file: Path, force_redownload: bool = False):
        self.links_file = links_file
        self.force_redownload = force_redownload
        self.download_log = DOWNLOAD_LOG
        
        # Setup logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # Initialize download log
        self._init_download_log()
        
    def _init_download_log(self):
        """Initialize the download log file"""
        with open(self.download_log, 'a') as f:
            f.write(f"\n=== SIMPLE DOWNLOAD SESSION STARTED: {datetime.now()} ===\n")
    
    def _log_to_file(self, message: str):
        """Log message to download log file"""
        with open(self.download_log, 'a') as f:
            f.write(f"[{datetime.now()}] {message}\n")
    
    def get_spotify_links(self) -> list:
        """Extract Spotify links from file, reversed order (bottom to top)"""
        if not self.links_file.exists():
            raise FileNotFoundError(f"Links file not found: {self.links_file}")
        
        with open(self.links_file, 'r') as f:
            lines = f.readlines()
        
        # Extract Spotify URLs and reverse order
        spotify_links = []
        for line in lines:
            line = line.strip()
            if line.startswith('https://open.spotify.com'):
                spotify_links.append(line)
        
        return list(reversed(spotify_links))  # Bottom to top as requested
    
    def download_single_link(self, link: str, album_num: int, total_albums: int) -> dict:
        """Download a single Spotify link with aggressive timeout"""
        start_time = time.time()
        
        progress_msg = f"[Album {album_num}/{total_albums}]"
        self.logger.info(f"{progress_msg} Starting: {link}")
        self._log_to_file(f"{progress_msg} Starting: {link}")
        
        # Build command
        cmd = [
            'python3', '-m', 'spotdl', '--config',
            '--threads', '1',  # Single thread
            '--overwrite', 'force' if self.force_redownload else 'skip',
            'download', link
        ]
        
        if not self.force_redownload:
            cmd.insert(-2, '--scan-for-songs')
        
        result = {
            'album_num': album_num,
            'link': link,
            'success': False,
            'downloaded': 0,
            'skipped': 0,
            'failed': 0,
            'expected': 0,
            'error': None,
            'duration': 0,
            'output': ''
        }
        
        for attempt in range(RETRY_COUNT + 1):
            if attempt > 0:
                self.logger.info(f"{progress_msg} Retry {attempt}/{RETRY_COUNT}")
                time.sleep(2)
            
            try:
                self.logger.info(f"{progress_msg} Running with {TIMEOUT_SECONDS}s hard timeout...")
                
                # Use subprocess.run with hard timeout - this WILL kill the process
                completed_process = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=TIMEOUT_SECONDS,  # Hard timeout
                    check=False
                )
                
                # Get output
                output_text = completed_process.stdout + completed_process.stderr
                result['output'] = output_text
                result['duration'] = time.time() - start_time
                
                # Print ALL output for debugging
                if output_text:
                    print(f"{progress_msg} === FULL OUTPUT START ===")
                    for line in output_text.split('\n'):
                        if line.strip():
                            print(f"{progress_msg} {line.strip()}")
                    print(f"{progress_msg} === FULL OUTPUT END ===")
                
                if completed_process.returncode == 0:
                    result['success'] = True
                    self._parse_output(result, output_text)
                    break
                else:
                    result['error'] = f"Process failed with return code {completed_process.returncode}"
                    # Check for rate limiting
                    if 'rate' in output_text.lower() or 'limit' in output_text.lower():
                        self.logger.warning(f"{progress_msg} Rate limit detected, pausing 60 seconds...")
                        time.sleep(60)
                    
            except subprocess.TimeoutExpired:
                result['error'] = f"HARD TIMEOUT after {TIMEOUT_SECONDS} seconds"
                self.logger.warning(f"{progress_msg} HARD TIMEOUT - process killed")
                break  # Don't retry timeouts
            except Exception as e:
                result['error'] = f"Exception: {str(e)}"
                self.logger.error(f"{progress_msg} Exception: {e}")
        
        # Log result
        if result['success']:
            self.logger.info(f"{progress_msg} ✅ SUCCESS - Downloaded: {result['downloaded']}, Skipped: {result['skipped']}")
            self._log_to_file(f"{progress_msg} SUCCESS: {link} - Downloaded: {result['downloaded']}, Skipped: {result['skipped']}, Failed: {result['failed']}")
        else:
            self.logger.error(f"{progress_msg} ❌ FAILED: {result['error']}")
            self._log_to_file(f"{progress_msg} FAILED: {link} - Error: {result['error']}")
        
        return result
    
    def _parse_output(self, result: dict, output_text: str):
        """Parse spotdl output to extract statistics"""
        import re
        
        # Look for "Found X songs in Album" pattern
        found_match = re.search(r'Found (\d+) songs in', output_text)
        if found_match:
            result['expected'] = int(found_match.group(1))
        
        # Count skipped songs
        result['skipped'] = len(re.findall(r'Skipping.*\(file already exists\)', output_text))
        
        # Count downloaded songs
        result['downloaded'] = len(re.findall(r'Downloaded.*\.flac', output_text))
        
        # Count failed songs
        result['failed'] = len(re.findall(r'Failed to download', output_text))
        
        # If we have expected but no counts, assume all skipped
        if result['expected'] > 0 and (result['downloaded'] + result['skipped'] + result['failed']) == 0:
            if 'file already exists' in output_text:
                result['skipped'] = result['expected']
    
    def run(self):
        """Main execution - process one album at a time"""
        try:
            links = self.get_spotify_links()
            if not links:
                self.logger.error("No Spotify links found in file")
                return
            
            total_albums = len(links)
            self.logger.info(f"🎵 Starting SIMPLE batch download")
            self.logger.info(f"📁 Source: {self.links_file}")
            self.logger.info(f"🎯 Found {total_albums} albums to process")
            self.logger.info(f"⚡ Processing ONE album at a time with {TIMEOUT_SECONDS}s hard timeout")
            self.logger.info(f"🔥 NO HANGING - processes will be killed aggressively")
            
            results = []
            total_downloaded = 0
            total_skipped = 0
            total_failed = 0
            
            # Process albums one by one
            for i, link in enumerate(links, 1):
                result = self.download_single_link(link, i, total_albums)
                results.append(result)
                
                # Update totals
                total_downloaded += result['downloaded']
                total_skipped += result['skipped']
                total_failed += result['failed']
                
                # Show running totals
                print(f"\n📊 Progress: {i}/{total_albums} albums processed")
                print(f"   🆕 NEW songs downloaded so far: {total_downloaded}")
                print(f"   ⏭️  Songs skipped so far: {total_skipped}")
                print(f"   ❌ Songs failed so far: {total_failed}")
                print("-" * 50)
                
                # Brief pause between albums to avoid rate limits
                if i < total_albums:
                    time.sleep(2)
            
            # Final summary
            self._display_summary(results, total_downloaded, total_skipped, total_failed)
            
        except Exception as e:
            self.logger.error(f"Fatal error: {e}")
            self._log_to_file(f"FATAL ERROR: {e}")
    
    def _display_summary(self, results: list, total_downloaded: int, total_skipped: int, total_failed: int):
        """Display final summary"""
        successful_albums = sum(1 for r in results if r['success'])
        failed_albums = len(results) - successful_albums
        
        print("\n" + "=" * 60)
        print("🎉 SIMPLE BATCH DOWNLOAD COMPLETE!")
        print("=" * 60)
        print("📊 FINAL SUMMARY:")
        print(f"   🆕 NEW SONGS DOWNLOADED: {total_downloaded}")
        print(f"   ⏭️  Songs already existed (skipped): {total_skipped}")
        print(f"   ❌ Songs failed to download: {total_failed}")
        print()
        print(f"   ✅ Albums processed successfully: {successful_albums}")
        print(f"   ❌ Albums failed: {failed_albums}")
        print("=" * 60)
        
        if total_downloaded > 0:
            print(f"🎉 SUCCESS: Downloaded {total_downloaded} NEW songs!")
        else:
            print("ℹ️  No new songs downloaded - all songs already exist")
        
        print(f"🎵 Check your Music folder: {MUSIC_DIR}")
        print(f"📝 Download log: {self.download_log}")
        
        # Log final summary
        self._log_to_file("=== SIMPLE DOWNLOAD SESSION COMPLETED ===")
        self._log_to_file(f"NEW songs downloaded: {total_downloaded}, Skipped: {total_skipped}, Failed: {total_failed}")
        self._log_to_file(f"Albums processed: {successful_albums}, Albums failed: {failed_albums}")
        self._log_to_file("")

def main():
    parser = argparse.ArgumentParser(description="🎵 Simple Spotify Batch Downloader - No Hanging!")
    parser.add_argument('file', nargs='?', default=DEFAULT_LINKS_FILE, 
                       help='Path to file containing Spotify links')
    parser.add_argument('--force', '-f', action='store_true',
                       help='Force redownload even if files exist')
    
    args = parser.parse_args()
    
    # Convert to Path object
    links_file = Path(args.file)
    
    # Create and run downloader
    downloader = SimpleSpotDL(
        links_file=links_file,
        force_redownload=args.force
    )
    
    downloader.run()

if __name__ == "__main__":
    main()
